//
//  main.swift
//  TestCAwigdet
//
//  Created by 王雷 on 10/6/25.
//

import Cocoa

// 创建应用程序实例
let app = NSApplication.shared

// 设置应用程序代理
class AppDelegate: NSObject, NSApplicationDelegate {
    var window: NSWindow!

    func applicationDidFinishLaunching(_ notification: Notification) {
        // 创建主窗口
        window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: 1000, height: 700),
            styleMask: [.titled, .closable, .miniaturizable, .resizable],
            backing: .buffered,
            defer: false
        )

        window.title = "TestCAwigdet - Core Animation UI Components"
        window.center()

        // 创建主视图控制器
        let viewController = ViewController()
        window.contentViewController = viewController

        // 显示窗口
        window.makeKeyAndOrderFront(nil)

        // 激活应用程序
        NSApp.activate(ignoringOtherApps: true)
    }

    func applicationShouldTerminateWhenLastWindowClosed(_ sender: NSApplication) -> <PERSON><PERSON> {
        return true
    }
}

// 设置应用程序代理
let delegate = AppDelegate()
app.delegate = delegate

// 运行应用程序
app.run()
