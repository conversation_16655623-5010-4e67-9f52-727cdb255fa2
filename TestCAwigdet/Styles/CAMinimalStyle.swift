//
//  CAMinimalStyle.swift
//  TestCAwigdet
//
//  Created by Core Animation UI Framework
//

import Cocoa
import QuartzCore

// MARK: - 简约风格配置
struct MinimalStyleConfig {
    // 颜色配置
    var primaryColor: NSColor = NSColor.black
    var secondaryColor: NSColor = NSColor.systemGray
    var backgroundColor: NSColor = NSColor.white
    var borderColor: NSColor = NSColor.lightGray
    var textColor: NSColor = NSColor.black
    var disabledColor: NSColor = NSColor.systemGray
    
    // 尺寸配置
    var cornerRadius: CGFloat = 8.0
    var borderWidth: CGFloat = 1.0
    
    // 动画配置
    var animationDuration: TimeInterval = 0.15
    var hoverScale: CGFloat = 1.02
    var pressScale: CGFloat = 0.98
    
    // 阴影配置
    var shadowOpacity: Float = 0.1
    var shadowRadius: CGFloat = 4.0
    var shadowOffset: CGSize = CGSize(width: 0, height: 2)
}

// MARK: - 简约风格应用器
class CAMinimalStyle {
    
    static let shared = CAMinimalStyle()
    
    private init() {}
    
    // MARK: - 应用简约风格
    func applyMinimalStyle(to layer: CALayer, config: MinimalStyleConfig = MinimalStyleConfig()) {
        // 清除现有子层
        layer.sublayers?.removeAll()
        
        // 设置基础属性
        layer.cornerRadius = config.cornerRadius
        layer.masksToBounds = false
        
        // 创建背景层
        let backgroundLayer = createBackgroundLayer(config: config)
        layer.insertSublayer(backgroundLayer, at: 0)
        
        // 创建边框层
        let borderLayer = createBorderLayer(config: config)
        layer.addSublayer(borderLayer)
        
        // 应用阴影
        applyShadow(to: layer, config: config)
    }
    
    // MARK: - 创建背景层
    private func createBackgroundLayer(config: MinimalStyleConfig) -> CAShapeLayer {
        let backgroundLayer = CAShapeLayer()
        backgroundLayer.fillColor = config.backgroundColor.cgColor
        backgroundLayer.strokeColor = NSColor.clear.cgColor
        return backgroundLayer
    }
    
    // MARK: - 创建边框层
    private func createBorderLayer(config: MinimalStyleConfig) -> CAShapeLayer {
        let borderLayer = CAShapeLayer()
        borderLayer.fillColor = NSColor.clear.cgColor
        borderLayer.strokeColor = config.borderColor.cgColor
        borderLayer.lineWidth = config.borderWidth
        return borderLayer
    }
    
    // MARK: - 应用阴影
    private func applyShadow(to layer: CALayer, config: MinimalStyleConfig) {
        layer.shadowColor = NSColor.black.cgColor
        layer.shadowOffset = config.shadowOffset
        layer.shadowRadius = config.shadowRadius
        layer.shadowOpacity = config.shadowOpacity
    }
    
    // MARK: - 更新层框架
    func updateLayerFrames(in parentLayer: CALayer, config: MinimalStyleConfig = MinimalStyleConfig()) {
        let bounds = parentLayer.bounds
        
        for sublayer in parentLayer.sublayers ?? [] {
            if let shapeLayer = sublayer as? CAShapeLayer {
                shapeLayer.frame = bounds
                let path = NSBezierPath(roundedRect: bounds, 
                                      xRadius: config.cornerRadius, 
                                      yRadius: config.cornerRadius)
                shapeLayer.path = path.cgPath
            }
        }
        
        // 更新阴影路径
        let shadowPath = NSBezierPath(roundedRect: bounds, 
                                     xRadius: config.cornerRadius, 
                                     yRadius: config.cornerRadius)
        parentLayer.shadowPath = shadowPath.cgPath
    }
    
    // MARK: - 交互动画
    func animateHover(layer: CALayer, isHovering: Bool, config: MinimalStyleConfig = MinimalStyleConfig()) {
        let scale = isHovering ? config.hoverScale : 1.0
        
        CATransaction.begin()
        CATransaction.setAnimationDuration(config.animationDuration)
        CATransaction.setAnimationTimingFunction(CAMediaTimingFunction(name: .easeOut))
        
        layer.transform = CATransform3DMakeScale(scale, scale, 1.0)
        
        CATransaction.commit()
    }
    
    func animatePress(layer: CALayer, isPressed: Bool, config: MinimalStyleConfig = MinimalStyleConfig()) {
        let scale = isPressed ? config.pressScale : 1.0
        
        CATransaction.begin()
        CATransaction.setAnimationDuration(config.animationDuration * 0.5)
        CATransaction.setAnimationTimingFunction(CAMediaTimingFunction(name: .easeOut))
        
        layer.transform = CATransform3DMakeScale(scale, scale, 1.0)
        
        CATransaction.commit()
    }
    
    // MARK: - 预设样式
    static func primaryButton() -> MinimalStyleConfig {
        var config = MinimalStyleConfig()
        config.backgroundColor = NSColor.black
        config.borderColor = NSColor.black
        config.textColor = NSColor.white
        return config
    }
    
    static func secondaryButton() -> MinimalStyleConfig {
        var config = MinimalStyleConfig()
        config.backgroundColor = NSColor.white
        config.borderColor = NSColor.lightGray
        config.textColor = NSColor.black
        return config
    }
    
    static func outlineButton() -> MinimalStyleConfig {
        var config = MinimalStyleConfig()
        config.backgroundColor = NSColor.clear
        config.borderColor = NSColor.black
        config.borderWidth = 1.5
        config.textColor = NSColor.black
        return config
    }
    
    static func ghostButton() -> MinimalStyleConfig {
        var config = MinimalStyleConfig()
        config.backgroundColor = NSColor.clear
        config.borderColor = NSColor.clear
        config.textColor = NSColor.systemGray
        config.shadowOpacity = 0.0
        return config
    }
    
    static func dangerButton() -> MinimalStyleConfig {
        var config = MinimalStyleConfig()
        config.backgroundColor = NSColor.systemRed
        config.borderColor = NSColor.systemRed
        config.textColor = NSColor.white
        return config
    }
    
    static func lightMode() -> MinimalStyleConfig {
        var config = MinimalStyleConfig()
        config.backgroundColor = NSColor.white
        config.borderColor = NSColor.lightGray
        config.textColor = NSColor.black
        config.shadowOpacity = 0.05
        return config
    }

    static func darkMode() -> MinimalStyleConfig {
        var config = MinimalStyleConfig()
        config.backgroundColor = NSColor.controlBackgroundColor
        config.borderColor = NSColor.darkGray
        config.textColor = NSColor.labelColor
        config.shadowOpacity = 0.2
        return config
    }
}
