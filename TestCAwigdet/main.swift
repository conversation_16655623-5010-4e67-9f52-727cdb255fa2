//
//  main.swift
//  TestCAwigdet
//
//  Created by 王雷 on 10/6/25.
//

import Cocoa

// 创建应用程序实例
let app = NSApplication.shared

// 设置应用程序代理
class AppDelegate: NSObject, NSApplicationDelegate {
    var window: NSWindow!

    func applicationDidFinishLaunching(_ notification: Notification) {
        // 获取屏幕尺寸
        guard let screen = NSScreen.main else { return }
        let screenRect = screen.frame  // 使用完整屏幕而不是可见区域

        print("Screen size: \(screenRect)")

        // 创建一个大窗口
        window = NSWindow(
            contentRect: NSRect(x: 0, y: 0, width: screenRect.width - 100, height: screenRect.height - 100),
            styleMask: [.titled, .closable, .miniaturizable, .resizable],
            backing: .buffered,
            defer: false
        )

        window.title = "TestCAwigdet - Core Animation UI Components"

        // 居中显示
        window.center()

        // 创建主视图控制器
        let viewController = ViewController()
        window.contentViewController = viewController

        // 显示窗口
        window.makeKeyAndOrderFront(nil)

        // 激活应用程序
        NSApp.activate(ignoringOtherApps: true)

        print("Window frame: \(window.frame)")
    }

    func applicationShouldTerminateWhenLastWindowClosed(_ sender: NSApplication) -> Bool {
        return true
    }
}

// 设置应用程序代理
let delegate = AppDelegate()
app.delegate = delegate

// 运行应用程序
app.run()
