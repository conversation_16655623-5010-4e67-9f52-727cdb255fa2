//
//  ButtonDemo.swift
//  TestCAwigdet
//
//  Created by Core Animation UI Framework
//

import Cocoa

class ButtonDemo: NSViewController {
    
    // MARK: - UI Elements
    private var scrollView: NSScrollView!
    private var contentView: NSView!
    private var stackView: NSStackView!
    
    // MARK: - Lifecycle
    override func loadView() {
        view = NSView(frame: NSRect(x: 0, y: 0, width: 800, height: 600))
        view.wantsLayer = true
        view.layer?.backgroundColor = NSColor.windowBackgroundColor.cgColor
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        createButtonExamples()
    }
    
    // MARK: - UI Setup
    private func setupUI() {
        // 创建滚动视图
        scrollView = NSScrollView()
        scrollView.translatesAutoresizingMaskIntoConstraints = false
        scrollView.hasVerticalScroller = true
        scrollView.hasHorizontalScroller = false
        scrollView.autohidesScrollers = true
        view.addSubview(scrollView)
        
        // 创建内容视图
        contentView = NSView()
        contentView.translatesAutoresizingMaskIntoConstraints = false
        scrollView.documentView = contentView
        
        // 创建垂直堆栈视图
        stackView = NSStackView()
        stackView.translatesAutoresizingMaskIntoConstraints = false
        stackView.orientation = .vertical
        stackView.spacing = 30
        stackView.alignment = .centerX
        contentView.addSubview(stackView)
        
        // 设置约束
        NSLayoutConstraint.activate([
            // 滚动视图约束
            scrollView.topAnchor.constraint(equalTo: view.topAnchor, constant: 20),
            scrollView.leadingAnchor.constraint(equalTo: view.leadingAnchor, constant: 20),
            scrollView.trailingAnchor.constraint(equalTo: view.trailingAnchor, constant: -20),
            scrollView.bottomAnchor.constraint(equalTo: view.bottomAnchor, constant: -20),
            
            // 内容视图约束
            contentView.topAnchor.constraint(equalTo: scrollView.topAnchor),
            contentView.leadingAnchor.constraint(equalTo: scrollView.leadingAnchor),
            contentView.trailingAnchor.constraint(equalTo: scrollView.trailingAnchor),
            contentView.widthAnchor.constraint(equalTo: scrollView.widthAnchor),
            
            // 堆栈视图约束
            stackView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 20),
            stackView.centerXAnchor.constraint(equalTo: contentView.centerXAnchor),
            stackView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -20),
            stackView.widthAnchor.constraint(lessThanOrEqualTo: contentView.widthAnchor, constant: -40)
        ])
    }
    
    // MARK: - Button Examples
    private func createButtonExamples() {
        // 标题
        let titleLabel = createLabel(text: "CAButton 组件演示", fontSize: 24, weight: .bold)
        stackView.addArrangedSubview(titleLabel)
        
        // 按钮类型演示
        createButtonTypeSection()
        
        // 按钮尺寸演示
        createButtonSizeSection()
        
        // 交互演示
        createInteractionSection()
        
        // 状态演示
        createStateSection()
    }
    
    private func createButtonTypeSection() {
        let sectionLabel = createLabel(text: "按钮类型", fontSize: 18, weight: .semibold)
        stackView.addArrangedSubview(sectionLabel)
        
        let horizontalStack = NSStackView()
        horizontalStack.orientation = .horizontal
        horizontalStack.spacing = 15
        horizontalStack.alignment = .centerY
        horizontalStack.distribution = .fillProportionally
        
        // 主要按钮
        let primaryButton = CAButton(title: "主要按钮", type: .primary, size: .medium)
        primaryButton.onTap = { [weak self] in
            self?.showAlert(message: "主要按钮被点击了！")
        }
        horizontalStack.addArrangedSubview(primaryButton)
        
        // 次要按钮
        let secondaryButton = CAButton(title: "次要按钮", type: .secondary, size: .medium)
        secondaryButton.onTap = { [weak self] in
            self?.showAlert(message: "次要按钮被点击了！")
        }
        horizontalStack.addArrangedSubview(secondaryButton)
        
        // 轮廓按钮
        let outlineButton = CAButton(title: "轮廓按钮", type: .outline, size: .medium)
        outlineButton.onTap = { [weak self] in
            self?.showAlert(message: "轮廓按钮被点击了！")
        }
        horizontalStack.addArrangedSubview(outlineButton)
        
        // 幽灵按钮
        let ghostButton = CAButton(title: "幽灵按钮", type: .ghost, size: .medium)
        ghostButton.onTap = { [weak self] in
            self?.showAlert(message: "幽灵按钮被点击了！")
        }
        horizontalStack.addArrangedSubview(ghostButton)
        
        // 危险按钮
        let dangerButton = CAButton(title: "危险按钮", type: .danger, size: .medium)
        dangerButton.onTap = { [weak self] in
            self?.showAlert(message: "危险按钮被点击了！")
        }
        horizontalStack.addArrangedSubview(dangerButton)
        
        stackView.addArrangedSubview(horizontalStack)
    }
    
    private func createButtonSizeSection() {
        let sectionLabel = createLabel(text: "按钮尺寸", fontSize: 18, weight: .semibold)
        stackView.addArrangedSubview(sectionLabel)
        
        let horizontalStack = NSStackView()
        horizontalStack.orientation = .horizontal
        horizontalStack.spacing = 15
        horizontalStack.alignment = .centerY
        
        // 小尺寸
        let smallButton = CAButton(title: "小按钮", type: .primary, size: .small)
        smallButton.onTap = { [weak self] in
            self?.showAlert(message: "小按钮被点击了！")
        }
        horizontalStack.addArrangedSubview(smallButton)
        
        // 中等尺寸
        let mediumButton = CAButton(title: "中等按钮", type: .primary, size: .medium)
        mediumButton.onTap = { [weak self] in
            self?.showAlert(message: "中等按钮被点击了！")
        }
        horizontalStack.addArrangedSubview(mediumButton)
        
        // 大尺寸
        let largeButton = CAButton(title: "大按钮", type: .primary, size: .large)
        largeButton.onTap = { [weak self] in
            self?.showAlert(message: "大按钮被点击了！")
        }
        horizontalStack.addArrangedSubview(largeButton)
        
        stackView.addArrangedSubview(horizontalStack)
    }
    
    private func createInteractionSection() {
        let sectionLabel = createLabel(text: "样式风格", fontSize: 18, weight: .semibold)
        stackView.addArrangedSubview(sectionLabel)

        let horizontalStack = NSStackView()
        horizontalStack.orientation = .horizontal
        horizontalStack.spacing = 15
        horizontalStack.alignment = .centerY

        // 简约风格按钮
        let minimalButton = CAButton(title: "简约风格", type: .primary, size: .medium)
        minimalButton.useMinimalStyle = true
        minimalButton.useGlassmorphism = false
        minimalButton.onTap = { [weak self] in
            self?.showAlert(message: "简约风格按钮被点击了！")
        }
        horizontalStack.addArrangedSubview(minimalButton)

        // 玻璃拟态按钮
        let glassButton = CAButton(title: "玻璃拟态", type: .primary, size: .medium)
        glassButton.useMinimalStyle = false
        glassButton.useGlassmorphism = true
        glassButton.onTap = { [weak self] in
            self?.showAlert(message: "玻璃拟态按钮被点击了！")
        }
        horizontalStack.addArrangedSubview(glassButton)

        // 传统样式按钮
        let traditionalButton = CAButton(title: "传统样式", type: .primary, size: .medium)
        traditionalButton.useMinimalStyle = false
        traditionalButton.useGlassmorphism = false
        traditionalButton.onTap = { [weak self] in
            self?.showAlert(message: "传统样式按钮被点击了！")
        }
        horizontalStack.addArrangedSubview(traditionalButton)

        stackView.addArrangedSubview(horizontalStack)
    }
    
    private func createStateSection() {
        let sectionLabel = createLabel(text: "按钮状态", fontSize: 18, weight: .semibold)
        stackView.addArrangedSubview(sectionLabel)
        
        let horizontalStack = NSStackView()
        horizontalStack.orientation = .horizontal
        horizontalStack.spacing = 15
        horizontalStack.alignment = .centerY
        
        // 正常状态
        let normalButton = CAButton(title: "正常状态", type: .primary, size: .medium)
        normalButton.onTap = { [weak self] in
            self?.showAlert(message: "正常状态按钮被点击了！")
        }
        horizontalStack.addArrangedSubview(normalButton)
        
        // 禁用状态
        let disabledButton = CAButton(title: "禁用状态", type: .primary, size: .medium)
        disabledButton.isEnabled = false
        horizontalStack.addArrangedSubview(disabledButton)
        
        // 选中状态
        let selectedButton = CAButton(title: "选中状态", type: .primary, size: .medium)
        selectedButton.isSelected = true
        selectedButton.onTap = { [weak self] in
            self?.showAlert(message: "选中状态按钮被点击了！")
        }
        horizontalStack.addArrangedSubview(selectedButton)
        
        stackView.addArrangedSubview(horizontalStack)
    }
    
    // MARK: - Helper Methods
    private func createLabel(text: String, fontSize: CGFloat, weight: NSFont.Weight = .regular) -> NSTextField {
        let label = NSTextField(labelWithString: text)
        label.font = NSFont.systemFont(ofSize: fontSize, weight: weight)
        label.textColor = NSColor.labelColor
        label.alignment = .center
        return label
    }
    
    private func showAlert(message: String) {
        let alert = NSAlert()
        alert.messageText = "按钮点击事件"
        alert.informativeText = message
        alert.alertStyle = .informational
        alert.addButton(withTitle: "确定")
        alert.runModal()
    }
}
