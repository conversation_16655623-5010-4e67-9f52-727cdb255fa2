//
//  CAButton.swift
//  TestCAwigdet
//
//  Created by Core Animation UI Framework
//

import Cocoa
import QuartzCore

// MARK: - 按钮类型枚举
enum CAButtonType {
    case primary    // 主要按钮
    case secondary  // 次要按钮
    case outline    // 轮廓按钮
    case ghost      // 幽灵按钮
    case danger     // 危险按钮
}

// MARK: - 按钮大小枚举
enum CAButtonSize {
    case small      // 小尺寸
    case medium     // 中等尺寸
    case large      // 大尺寸
    
    var height: CGFloat {
        switch self {
        case .small: return 28
        case .medium: return 36
        case .large: return 44
        }
    }
    
    var fontSize: CGFloat {
        switch self {
        case .small: return 12
        case .medium: return 14
        case .large: return 16
        }
    }
    
    var padding: CGFloat {
        switch self {
        case .small: return 12
        case .medium: return 16
        case .large: return 20
        }
    }
}

// MARK: - CAButton 类
class CAButton: CAUIBase {
    
    // MARK: - Properties
    var title: String = "" {
        didSet {
            updateTitle()
        }
    }
    
    var buttonType: CAButtonType = .primary {
        didSet {
            updateAppearance()
        }
    }
    
    var buttonSize: CAButtonSize = .medium {
        didSet {
            updateSize()
            updateAppearance()
        }
    }
    
    var useGlassmorphism: Bool = false {
        didSet {
            updateAppearance()
        }
    }

    var useMinimalStyle: Bool = true {
        didSet {
            updateAppearance()
        }
    }
    
    // 回调闭包
    var onTap: (() -> Void)?
    
    // MARK: - Layer Properties
    private var textLayer: CATextLayer!
    private var iconLayer: CAShapeLayer?
    private var glassmorphismConfig: GlassmorphismConfig!
    private var minimalConfig: MinimalStyleConfig!
    
    // MARK: - Initialization
    convenience init(title: String, type: CAButtonType = .primary, size: CAButtonSize = .medium) {
        self.init(frame: .zero)
        self.title = title
        self.buttonType = type
        self.buttonSize = size
        updateSize()
    }
    
    override func setupLayers() {
        super.setupLayers()

        // 设置样式配置
        glassmorphismConfig = getGlassmorphismConfig()
        minimalConfig = getMinimalStyleConfig()

        // 创建文本层
        setupTextLayer()

        // 应用样式效果
        if useMinimalStyle {
            applyMinimalStyle()
        } else if useGlassmorphism {
            applyGlassmorphismStyle()
        } else {
            // 默认样式下也要添加文本层
            layer?.addSublayer(textLayer)
        }

        // 确保文本层在最上层并更新内容
        updateTitle()
    }
    
    // MARK: - Text Layer Setup
    private func setupTextLayer() {
        textLayer = CATextLayer()
        textLayer.alignmentMode = .center
        textLayer.isWrapped = false
        textLayer.truncationMode = .end
        textLayer.contentsScale = NSScreen.main?.backingScaleFactor ?? 2.0

        // 设置字体
        let font = NSFont.systemFont(ofSize: buttonSize.fontSize, weight: .medium)
        textLayer.font = font
        textLayer.fontSize = buttonSize.fontSize

        // 设置文本内容
        textLayer.string = title

        // 暂时设置为黑色，后面会在updateTitle中更新
        textLayer.foregroundColor = NSColor.black.cgColor

        // 不在这里添加到父层，等样式应用后再添加
    }
    
    // MARK: - Size Management
    private func updateSize() {
        let height = buttonSize.height
        let padding = buttonSize.padding

        // 计算文本宽度
        let titleWidth = title.size(withAttributes: [
            .font: NSFont.systemFont(ofSize: buttonSize.fontSize, weight: .medium)
        ]).width

        let width = max(titleWidth + padding * 2, height * 2) // 最小宽度为高度的2倍

        // 移除旧的宽度约束
        let oldWidthConstraints = self.constraints.filter { $0.firstAttribute == .width }
        NSLayoutConstraint.deactivate(oldWidthConstraints)

        // 移除旧的高度约束
        let oldHeightConstraints = self.constraints.filter { $0.firstAttribute == .height }
        NSLayoutConstraint.deactivate(oldHeightConstraints)

        // 设置新的约束，使用优先级避免冲突
        let widthConstraint = widthAnchor.constraint(equalToConstant: width)
        widthConstraint.priority = NSLayoutConstraint.Priority(999) // 高优先级但不是必需的
        widthConstraint.isActive = true

        let heightConstraint = heightAnchor.constraint(equalToConstant: height)
        heightConstraint.priority = NSLayoutConstraint.Priority(1000) // 高优先级
        heightConstraint.isActive = true
    }
    
    // MARK: - Title Management
    private func updateTitle() {
        guard let textLayer = textLayer else { return }

        textLayer.string = title

        // 确保配置已初始化
        if minimalConfig == nil {
            minimalConfig = getMinimalStyleConfig()
        }
        if glassmorphismConfig == nil {
            glassmorphismConfig = getGlassmorphismConfig()
        }

        // 设置文本颜色，确保可见
        let textColor = getTextColor()
        textLayer.foregroundColor = textColor.cgColor

        // 确保文本层在最上层
        if let parentLayer = layer {
            textLayer.removeFromSuperlayer()
            parentLayer.addSublayer(textLayer)
            textLayer.frame = bounds
        }

        // 更新尺寸
        updateSize()
    }
    
    // MARK: - Minimal Style
    private func applyMinimalStyle() {
        guard let layer = self.layer else { return }

        // 清除默认样式
        backgroundLayer.removeFromSuperlayer()
        borderLayer.removeFromSuperlayer()
        shadowLayer.removeFromSuperlayer()

        // 应用简约风格
        CAMinimalStyle.shared.applyMinimalStyle(to: layer, config: minimalConfig)

        // 确保文本层在最上层
        if let textLayer = textLayer {
            textLayer.removeFromSuperlayer()
            layer.addSublayer(textLayer)
            // 更新文本层的frame
            textLayer.frame = bounds
        }
    }

    // MARK: - Glassmorphism Style
    private func applyGlassmorphismStyle() {
        guard let layer = self.layer else { return }

        // 清除默认样式
        backgroundLayer.removeFromSuperlayer()
        borderLayer.removeFromSuperlayer()
        shadowLayer.removeFromSuperlayer()

        // 应用玻璃拟态效果
        CAGlassmorphism.shared.applyGlassmorphism(to: layer, config: glassmorphismConfig)

        // 确保文本层在最上层
        if let textLayer = textLayer {
            textLayer.removeFromSuperlayer()
            layer.addSublayer(textLayer)
            // 更新文本层的frame
            textLayer.frame = bounds
        }
    }
    
    private func getMinimalStyleConfig() -> MinimalStyleConfig {
        switch buttonType {
        case .primary:
            return CAMinimalStyle.primaryButton()
        case .secondary:
            return CAMinimalStyle.secondaryButton()
        case .outline:
            return CAMinimalStyle.outlineButton()
        case .ghost:
            return CAMinimalStyle.ghostButton()
        case .danger:
            return CAMinimalStyle.dangerButton()
        }
    }

    private func getGlassmorphismConfig() -> GlassmorphismConfig {
        switch buttonType {
        case .primary:
            return CAGlassmorphism.accentGlass()
        case .secondary:
            return CAGlassmorphism.lightGlass()
        case .outline:
            var config = CAGlassmorphism.lightGlass()
            config.backgroundColor = NSColor.clear
            config.borderWidth = 2.0
            return config
        case .ghost:
            var config = CAGlassmorphism.lightGlass()
            config.backgroundColor = NSColor.clear
            config.borderColor = NSColor.clear
            return config
        case .danger:
            return CAGlassmorphism.coloredGlass(color: .systemRed)
        }
    }
    
    private func getTextColor() -> NSColor {
        if !isEnabled {
            return NSColor.disabledControlTextColor
        }

        if useMinimalStyle {
            return minimalConfig.textColor
        } else {
            switch buttonType {
            case .primary, .danger:
                return NSColor.white
            case .secondary, .outline, .ghost:
                return NSColor.controlTextColor
            }
        }
    }
    
    // MARK: - Layout
    override func updateLayerFrames() {
        super.updateLayerFrames()

        // 更新文本层位置
        if let textLayer = textLayer {
            textLayer.frame = bounds
        }

        // 更新样式层
        if useMinimalStyle, let layer = self.layer {
            CAMinimalStyle.shared.updateLayerFrames(in: layer, config: minimalConfig)
        } else if useGlassmorphism, let layer = self.layer {
            CAGlassmorphism.shared.updateLayerFrames(in: layer)
        }
    }
    
    // MARK: - Appearance Updates
    override func updateAppearance() {
        if useMinimalStyle {
            minimalConfig = getMinimalStyleConfig()
            applyMinimalStyle()
        } else if useGlassmorphism {
            glassmorphismConfig = getGlassmorphismConfig()
            applyGlassmorphismStyle()
        } else {
            super.updateAppearance()
        }

        updateTitle()
    }
    
    // MARK: - Animation
    override func animateToState(_ state: CAUIState) {
        guard let layer = self.layer else { return }

        if useMinimalStyle {
            switch state {
            case .highlighted:
                CAMinimalStyle.shared.animateHover(layer: layer, isHovering: true, config: minimalConfig)
            case .normal:
                CAMinimalStyle.shared.animateHover(layer: layer, isHovering: false, config: minimalConfig)
            default:
                break
            }
        } else if useGlassmorphism {
            switch state {
            case .highlighted:
                CAGlassmorphism.shared.animateHover(layer: layer, isHovering: true, config: glassmorphismConfig)
            case .normal:
                CAGlassmorphism.shared.animateHover(layer: layer, isHovering: false, config: glassmorphismConfig)
            default:
                break
            }
        } else {
            super.animateToState(state)
        }
    }
    
    // MARK: - Mouse Events
    override func mouseDown(with event: NSEvent) {
        super.mouseDown(with: event)

        if isEnabled {
            if useMinimalStyle {
                CAMinimalStyle.shared.animatePress(layer: layer!, isPressed: true, config: minimalConfig)
            } else if useGlassmorphism {
                CAGlassmorphism.shared.animatePress(layer: layer!, isPressed: true, config: glassmorphismConfig)
            }
        }
    }

    override func mouseUp(with event: NSEvent) {
        super.mouseUp(with: event)

        if isEnabled {
            if useMinimalStyle {
                CAMinimalStyle.shared.animatePress(layer: layer!, isPressed: false, config: minimalConfig)
            } else if useGlassmorphism {
                CAGlassmorphism.shared.animatePress(layer: layer!, isPressed: false, config: glassmorphismConfig)
            }

            // 检查是否在按钮范围内
            let location = convert(event.locationInWindow, from: nil)
            if bounds.contains(location) {
                onTap?()
            }
        }
    }
}
