//
//  CAButton.swift
//  TestCAwigdet
//
//  Created by Core Animation UI Framework
//

import Cocoa
import QuartzCore

// MARK: - 按钮类型枚举
enum CAButtonType {
    case primary    // 主要按钮
    case secondary  // 次要按钮
    case outline    // 轮廓按钮
    case ghost      // 幽灵按钮
    case danger     // 危险按钮
}

// MARK: - 按钮大小枚举
enum CAButtonSize {
    case small      // 小尺寸
    case medium     // 中等尺寸
    case large      // 大尺寸
    
    var height: CGFloat {
        switch self {
        case .small: return 28
        case .medium: return 36
        case .large: return 44
        }
    }
    
    var fontSize: CGFloat {
        switch self {
        case .small: return 12
        case .medium: return 14
        case .large: return 16
        }
    }
    
    var padding: CGFloat {
        switch self {
        case .small: return 12
        case .medium: return 16
        case .large: return 20
        }
    }
}

// MARK: - CAButton 类
class CAButton: CAUIBase {
    
    // MARK: - Properties
    var title: String = "" {
        didSet {
            updateTitle()
        }
    }
    
    var buttonType: CAButtonType = .primary {
        didSet {
            updateAppearance()
        }
    }
    
    var buttonSize: CAButtonSize = .medium {
        didSet {
            updateSize()
            updateAppearance()
        }
    }
    
    var useGlassmorphism: Bool = true {
        didSet {
            updateAppearance()
        }
    }
    
    // 回调闭包
    var onTap: (() -> Void)?
    
    // MARK: - Layer Properties
    private var textLayer: CATextLayer!
    private var iconLayer: CAShapeLayer?
    private var glassmorphismConfig: GlassmorphismConfig!
    
    // MARK: - Initialization
    convenience init(title: String, type: CAButtonType = .primary, size: CAButtonSize = .medium) {
        self.init(frame: .zero)
        self.title = title
        self.buttonType = type
        self.buttonSize = size
        updateSize()
    }
    
    override func setupLayers() {
        super.setupLayers()
        
        // 设置玻璃拟态配置
        glassmorphismConfig = getGlassmorphismConfig()
        
        // 创建文本层
        setupTextLayer()
        
        // 应用玻璃拟态效果
        if useGlassmorphism {
            applyGlassmorphismStyle()
        }
    }
    
    // MARK: - Text Layer Setup
    private func setupTextLayer() {
        textLayer = CATextLayer()
        textLayer.alignmentMode = .center
        textLayer.isWrapped = false
        textLayer.truncationMode = .end
        textLayer.contentsScale = NSScreen.main?.backingScaleFactor ?? 2.0
        
        // 设置字体
        let font = NSFont.systemFont(ofSize: buttonSize.fontSize, weight: .medium)
        textLayer.font = font
        textLayer.fontSize = buttonSize.fontSize
        
        layer?.addSublayer(textLayer)
        updateTitle()
    }
    
    // MARK: - Size Management
    private func updateSize() {
        let height = buttonSize.height
        let padding = buttonSize.padding
        
        // 计算文本宽度
        let titleWidth = title.size(withAttributes: [
            .font: NSFont.systemFont(ofSize: buttonSize.fontSize, weight: .medium)
        ]).width
        
        let width = max(titleWidth + padding * 2, height * 2) // 最小宽度为高度的2倍
        
        // 更新约束或frame
        if let constraints = self.constraints.first(where: { $0.firstAttribute == .width }) {
            constraints.constant = width
        } else {
            widthAnchor.constraint(equalToConstant: width).isActive = true
        }
        
        if let constraints = self.constraints.first(where: { $0.firstAttribute == .height }) {
            constraints.constant = height
        } else {
            heightAnchor.constraint(equalToConstant: height).isActive = true
        }
    }
    
    // MARK: - Title Management
    private func updateTitle() {
        guard let textLayer = textLayer else { return }
        
        textLayer.string = title
        textLayer.foregroundColor = getTextColor().cgColor
        
        // 更新尺寸
        updateSize()
    }
    
    // MARK: - Glassmorphism Style
    private func applyGlassmorphismStyle() {
        guard let layer = self.layer else { return }
        
        // 清除默认样式
        backgroundLayer.removeFromSuperlayer()
        borderLayer.removeFromSuperlayer()
        shadowLayer.removeFromSuperlayer()
        
        // 应用玻璃拟态效果
        CAGlassmorphism.shared.applyGlassmorphism(to: layer, config: glassmorphismConfig)
    }
    
    private func getGlassmorphismConfig() -> GlassmorphismConfig {
        switch buttonType {
        case .primary:
            return CAGlassmorphism.accentGlass()
        case .secondary:
            return CAGlassmorphism.lightGlass()
        case .outline:
            var config = CAGlassmorphism.lightGlass()
            config.backgroundColor = NSColor.clear
            config.borderWidth = 2.0
            return config
        case .ghost:
            var config = CAGlassmorphism.lightGlass()
            config.backgroundColor = NSColor.clear
            config.borderColor = NSColor.clear
            return config
        case .danger:
            return CAGlassmorphism.coloredGlass(color: .systemRed)
        }
    }
    
    private func getTextColor() -> NSColor {
        if !isEnabled {
            return NSColor.disabledControlTextColor
        }
        
        switch buttonType {
        case .primary, .danger:
            return NSColor.white
        case .secondary, .outline, .ghost:
            return NSColor.controlTextColor
        }
    }
    
    // MARK: - Layout
    override func updateLayerFrames() {
        super.updateLayerFrames()
        
        // 更新文本层位置
        if let textLayer = textLayer {
            textLayer.frame = bounds
        }
        
        // 更新玻璃拟态层
        if useGlassmorphism, let layer = self.layer {
            CAGlassmorphism.shared.updateLayerFrames(in: layer)
        }
    }
    
    // MARK: - Appearance Updates
    override func updateAppearance() {
        if useGlassmorphism {
            glassmorphismConfig = getGlassmorphismConfig()
            applyGlassmorphismStyle()
        } else {
            super.updateAppearance()
        }
        
        updateTitle()
    }
    
    // MARK: - Animation
    override func animateToState(_ state: CAUIState) {
        if useGlassmorphism {
            guard let layer = self.layer else { return }
            
            switch state {
            case .highlighted:
                CAGlassmorphism.shared.animateHover(layer: layer, isHovering: true, config: glassmorphismConfig)
            case .normal:
                CAGlassmorphism.shared.animateHover(layer: layer, isHovering: false, config: glassmorphismConfig)
            default:
                break
            }
        } else {
            super.animateToState(state)
        }
    }
    
    // MARK: - Mouse Events
    override func mouseDown(with event: NSEvent) {
        super.mouseDown(with: event)
        
        if isEnabled && useGlassmorphism {
            CAGlassmorphism.shared.animatePress(layer: layer!, isPressed: true, config: glassmorphismConfig)
        }
    }
    
    override func mouseUp(with event: NSEvent) {
        super.mouseUp(with: event)
        
        if isEnabled {
            if useGlassmorphism {
                CAGlassmorphism.shared.animatePress(layer: layer!, isPressed: false, config: glassmorphismConfig)
            }
            
            // 检查是否在按钮范围内
            let location = convert(event.locationInWindow, from: nil)
            if bounds.contains(location) {
                onTap?()
            }
        }
    }
}
